# 🎯 Ejercicios Prácticos - POO en C#

## Ejercicios Básicos

### 1. **Modificar Propiedades**
- Cambia la edad de un estudiante y observa las validaciones
- Intenta asignar valores inválidos y maneja las excepciones
- Modifica el nombre de un profesor y verifica que se actualice correctamente

### 2. **Experimentar con Calificaciones**
- Agrega más calificaciones a los estudiantes
- Crea un estudiante con promedio bajo y observa el estado académico
- Intenta agregar calificaciones fuera del rango válido (0-5)

### 3. **Gestión de Materias**
- Asigna más materias al profesor
- Intenta asignar la misma materia dos veces
- Remueve una materia y verifica que se actualice la carga académica

## Ejercicios Intermedios

### 4. **Crear N<PERSON>s Instan<PERSON>**
```csharp
// Crea un estudiante de Medicina con las siguientes características:
// Nombre: "<PERSON>", Edad: 22, ID: "98765432"
// Número: "MED001", Semestre: 6, Cuota: 3500000

// Agrega calificaciones: 4.8, 4.5, 4.9, 4.7
// Verifica su estado académico y cuota con descuento
```

### 5. **Crear un Profesor Senior**
```csharp
// Crea un profesor con 25 años de experiencia
// Asígnale 5 materias diferentes
// Calcula su salario con todas las bonificaciones
```

### 6. **Polimorfismo Avanzado**
```csharp
// Crea una lista de 5 personas (3 estudiantes, 2 profesores)
// Usa un bucle para mostrar información de todos
// Calcula el total de cuotas/salarios
```

## Ejercicios Avanzados

### 7. **Extender la Funcionalidad**
Agrega las siguientes características a la clase `Estudiante`:

```csharp
// Método para calcular el promedio de un semestre específico
public double CalcularPromedioSemestre(int semestre, List<double> calificacionesSemestre)

// Propiedad para determinar si puede graduarse (promedio >= 3.5 y semestre >= 8)
public bool PuedeGraduarse { get; }

// Método para aplicar beca (descuento adicional del 50% si promedio >= 4.5)
public decimal CalcularCuotaConBeca()
```

### 8. **Nueva Clase Derivada**
Crea una nueva clase `Administrativo` que herede de `Persona`:

```csharp
public class Administrativo : Persona
{
    private string _cargo;
    private string _departamento;
    private decimal _salarioBase;
    private List<string> _responsabilidades;
    
    // Implementa los métodos abstractos
    // Agrega funcionalidades específicas como:
    // - AsignarResponsabilidad()
    // - CalcularBonoProductividad()
    // - MostrarResponsabilidades()
}
```

### 9. **Sistema de Validaciones Avanzado**
Implementa validaciones más complejas:

```csharp
// En Estudiante: validar que el semestre sea coherente con la edad
// En Profesor: validar que los años de experiencia sean coherentes con la edad
// Agregar validación de formato para identificación (ej: solo números)
```

### 10. **Interfaz para Comportamientos**
Crea interfaces para comportamientos específicos:

```csharp
public interface IEvaluable
{
    double CalcularRendimiento();
    string ObtenerCategoria();
}

public interface IPagable
{
    decimal CalcularPagoMensual();
    DateTime ObtenerFechaPago();
}

// Implementa estas interfaces en las clases correspondientes
```

## Ejercicios de Diseño

### 11. **Patrón Factory**
Crea una clase `PersonaFactory` para crear instancias:

```csharp
public static class PersonaFactory
{
    public static Estudiante CrearEstudiante(/* parámetros */)
    public static Profesor CrearProfesor(/* parámetros */)
    public static Persona CrearPersonaAleatoria()
}
```

### 12. **Sistema de Reportes**
Implementa un sistema de reportes:

```csharp
public class ReporteAcademico
{
    public void GenerarReporteEstudiante(Estudiante estudiante)
    public void GenerarReporteProfesor(Profesor profesor)
    public void GenerarReporteGeneral(List<Persona> personas)
}
```

### 13. **Persistencia de Datos**
Agrega funcionalidad para guardar y cargar datos:

```csharp
// Métodos para serializar objetos a JSON
// Métodos para cargar datos desde archivos
// Sistema de backup automático
```

## Desafíos Creativos

### 14. **Sistema de Notificaciones**
- Implementa eventos para notificar cambios importantes
- Ej: cuando un estudiante cambia de semestre, cuando se asigna una nueva materia

### 15. **Calculadora de Estadísticas**
- Crea métodos estáticos para calcular estadísticas generales
- Promedio de edad de todos los estudiantes
- Profesor con más materias asignadas
- Estudiante con mejor promedio

### 16. **Sistema de Búsqueda**
```csharp
// Buscar estudiantes por carrera
// Buscar profesores por departamento
// Buscar personas por rango de edad
// Filtros combinados
```

## Preguntas de Reflexión

1. **¿Por qué la clase `Persona` es abstracta?**
2. **¿Qué ventajas tiene usar propiedades en lugar de campos públicos?**
3. **¿Cómo se demuestra el polimorfismo en este proyecto?**
4. **¿Qué pasaría si intentaras crear una instancia directa de `Persona`?**
5. **¿Por qué algunos métodos son `virtual` y otros `abstract`?**

## Extensiones Sugeridas

### Para Principiantes:
- Agregar más validaciones
- Crear más instancias de prueba
- Experimentar con diferentes valores

### Para Intermedios:
- Implementar nuevas clases derivadas
- Agregar interfaces
- Crear sistemas de reportes

### Para Avanzados:
- Implementar patrones de diseño
- Agregar persistencia de datos
- Crear una interfaz gráfica simple

---

**💡 Consejo**: Empieza con los ejercicios básicos y ve avanzando gradualmente. Cada ejercicio te ayudará a entender mejor los conceptos de POO.

**🎯 Objetivo**: Al completar estos ejercicios, tendrás una comprensión sólida de herencia, encapsulamiento y abstracción en C#.
