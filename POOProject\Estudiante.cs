using System;
using System.Collections.Generic;
using System.Linq;

namespace POOProject
{
    /// <summary>
    /// Clase que representa un Estudiante
    /// Demuestra HERENCIA al heredar de la clase Persona
    /// Demuestra ENCAPSULAMIENTO con propiedades específicas del estudiante
    /// Implementa métodos abstractos de la clase base
    /// </summary>
    public class Estudiante : Persona
    {
        // Campos privados específicos del estudiante - ENCAPSULAMIENTO
        private string _numeroEstudiante;
        private string _carrera;
        private int _semestre;
        private List<double> _calificaciones;
        private decimal _cuotaSemestral;

        // Constructor que llama al constructor de la clase base
        public Estudiante(string nombre, string apellido, int edad, string identificacion, 
                         string numeroEstudiante, string carrera, int semestre, decimal cuotaSemestral)
            : base(nombre, apellido, edad, identificacion) // Llamada al constructor base - HERENCIA
        {
            // Validar edad mínima para estudiantes
            ValidarEdadMinima(16); // Usando método protegido de la clase base

            _numeroEstudiante = numeroEstudiante ?? throw new ArgumentNullException(nameof(numeroEstudiante));
            _carrera = carrera ?? throw new ArgumentNullException(nameof(carrera));
            _semestre = semestre;
            _cuotaSemestral = cuotaSemestral;
            _calificaciones = new List<double>();
        }

        // Propiedades específicas del estudiante con encapsulamiento
        public string NumeroEstudiante
        {
            get { return _numeroEstudiante; }
            private set { _numeroEstudiante = value; } // Solo lectura desde fuera de la clase
        }

        public string Carrera
        {
            get { return _carrera; }
            set 
            { 
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("La carrera no puede estar vacía");
                _carrera = value; 
            }
        }

        public int Semestre
        {
            get { return _semestre; }
            set 
            { 
                if (value < 1 || value > 12)
                    throw new ArgumentException("El semestre debe estar entre 1 y 12");
                _semestre = value; 
            }
        }

        public decimal CuotaSemestral
        {
            get { return _cuotaSemestral; }
            set 
            { 
                if (value < 0)
                    throw new ArgumentException("La cuota semestral no puede ser negativa");
                _cuotaSemestral = value; 
            }
        }

        // Propiedad calculada - promedio de calificaciones
        public double PromedioCalificaciones
        {
            get
            {
                if (_calificaciones.Count == 0)
                    return 0.0;
                return _calificaciones.Average();
            }
        }

        // Propiedad de solo lectura para el estado académico
        public string EstadoAcademico
        {
            get
            {
                double promedio = PromedioCalificaciones;
                if (promedio >= 4.0) return "Excelente";
                if (promedio >= 3.5) return "Bueno";
                if (promedio >= 3.0) return "Regular";
                if (promedio >= 2.0) return "Deficiente";
                return "Reprobado";
            }
        }

        // Implementación del método abstracto de la clase base - ABSTRACCIÓN
        public override void MostrarRol()
        {
            Console.WriteLine($"Rol: Estudiante de {_carrera}");
            Console.WriteLine($"Número de estudiante: {_numeroEstudiante}");
            Console.WriteLine($"Semestre actual: {_semestre}");
        }

        // Implementación del método abstracto para calcular cuota - ABSTRACCIÓN
        public override decimal CalcularCuota()
        {
            // Aplicar descuento por buen rendimiento académico
            decimal descuento = 0;
            double promedio = PromedioCalificaciones;
            
            if (promedio >= 4.5)
                descuento = 0.20m; // 20% de descuento
            else if (promedio >= 4.0)
                descuento = 0.10m; // 10% de descuento
            else if (promedio >= 3.5)
                descuento = 0.05m; // 5% de descuento

            return _cuotaSemestral * (1 - descuento);
        }

        // Sobrescribir el método virtual de la clase base - HERENCIA
        public override void MostrarInformacion()
        {
            base.MostrarInformacion(); // Llamar al método de la clase base
            Console.WriteLine($"Carrera: {_carrera}");
            Console.WriteLine($"Semestre: {_semestre}");
            Console.WriteLine($"Número de estudiante: {_numeroEstudiante}");
            Console.WriteLine($"Promedio: {PromedioCalificaciones:F2}");
            Console.WriteLine($"Estado académico: {EstadoAcademico}");
            Console.WriteLine($"Cuota a pagar: ${CalcularCuota():F2}");
        }

        // Métodos específicos del estudiante
        public void AgregarCalificacion(double calificacion)
        {
            if (calificacion < 0 || calificacion > 5)
                throw new ArgumentException("La calificación debe estar entre 0 y 5");
            
            _calificaciones.Add(calificacion);
        }

        public void MostrarCalificaciones()
        {
            Console.WriteLine("\n--- Calificaciones ---");
            if (_calificaciones.Count == 0)
            {
                Console.WriteLine("No hay calificaciones registradas");
                return;
            }

            for (int i = 0; i < _calificaciones.Count; i++)
            {
                Console.WriteLine($"Materia {i + 1}: {_calificaciones[i]:F1}");
            }
            Console.WriteLine($"Promedio general: {PromedioCalificaciones:F2}");
        }

        public bool EstaEnPeligroAcademico()
        {
            return PromedioCalificaciones < 3.0;
        }

        // Método para cambiar de semestre
        public void AvanzarSemestre()
        {
            if (PromedioCalificaciones >= 3.0)
            {
                _semestre++;
                Console.WriteLine($"¡Felicitaciones! Has avanzado al semestre {_semestre}");
            }
            else
            {
                Console.WriteLine("No puedes avanzar de semestre. Promedio insuficiente.");
            }
        }
    }
}
