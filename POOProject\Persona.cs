using System;

namespace POOProject
{
    /// <summary>
    /// Clase abstracta que representa una Persona
    /// Demuestra ABSTRACCIÓN al ser una clase base abstracta
    /// Demuestra ENCAPSULAMIENTO con propiedades privadas y métodos públicos
    /// </summary>
    public abstract class Persona
    {
        // Campos privados - ENCAPSULAMIENTO
        private string _nombre;
        private string _apellido;
        private int _edad;
        private string _identificacion;

        // Constructor protegido para que solo las clases derivadas puedan instanciarla
        protected Persona(string nombre, string apellido, int edad, string identificacion)
        {
            _nombre = nombre ?? throw new ArgumentNullException(nameof(nombre));
            _apellido = apellido ?? throw new ArgumentNullException(nameof(apellido));
            _edad = edad;
            _identificacion = identificacion ?? throw new ArgumentNullException(nameof(identificacion));
        }

        // Propiedades públicas con encapsulamiento
        public string Nombre
        {
            get { return _nombre; }
            set 
            { 
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("El nombre no puede estar vacío");
                _nombre = value; 
            }
        }

        public string Apellido
        {
            get { return _apellido; }
            set 
            { 
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("El apellido no puede estar vacío");
                _apellido = value; 
            }
        }

        public int Edad
        {
            get { return _edad; }
            set 
            { 
                if (value < 0 || value > 100)
                    throw new ArgumentException("La edad debe estar entre 0 y 150 años");
                _edad = value; 
            }
        }

        public string Identificacion
        {
            get { return _identificacion; }
            protected set { _identificacion = value; } // Solo las clases derivadas pueden modificar
        }

        // Propiedad calculada - solo lectura
        public string NombreCompleto => $"{_nombre} {_apellido}";

        // Método virtual que puede ser sobrescrito por las clases derivadas
        public virtual void MostrarInformacion()
        {
            Console.WriteLine($"Nombre: {NombreCompleto}");
            Console.WriteLine($"Edad: {_edad} años");
            Console.WriteLine($"Identificación: {_identificacion}");
        }

        // Método abstracto que DEBE ser implementado por las clases derivadas - ABSTRACCIÓN
        public abstract void MostrarRol();

        // Método abstracto para calcular algún tipo de cuota o pago
        public abstract decimal CalcularCuota();

        // Método protegido que solo pueden usar las clases derivadas
        protected void ValidarEdadMinima(int edadMinima)
        {
            if (_edad < edadMinima)
                throw new InvalidOperationException($"La edad mínima requerida es {edadMinima} años");
        }

        // Sobrescribir ToString para una representación en cadena
        public override string ToString()
        {
            return $"{GetType().Name}: {NombreCompleto} ({_edad} años)";
        }
    }
}
