using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;

public class Grupo
{
    private string _idGrupo { get; set; } = string.Empty;
    private List<EstudianteAbstracto> _estudiantes { get; set; }= [];

    public string IdGrupo
    {
        get { return _idGrupo; }
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("El id del grupo no puede estar vacío");
            _idGrupo = value;
        }
    }

    public Grupo(string idGrupo)
    {
        IdGrupo = idGrupo;
    }

    public List<EstudianteAbstracto> RegistroEstudiantes() // Devuelve los estudiantes en orden alfabetico de la A->Z
    {
        try
        {
            _estudiantes.Sort((x, y) => x.NombreCompleto.CompareTo(y.NombreCompleto));
            return _estudiantes;

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            return new List<EstudianteAbstracto>();

        }
 
      
    }

}

public abstract class PersonaAbstracta
{
    protected string _nombreCompleto { get; set; } = string.Empty;
    protected DateOnly _fechaNacimiento { get; set; }

    public string NombreCompleto
    {
        get { return _nombreCompleto; }
        set
        {
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
                throw new ArgumentException("El Nombre no puede estar vacío");
            _nombreCompleto = value;
        }
    }
    public DateOnly FechaNacimiento
    {
        get { return _fechaNacimiento; }
        set
        {
            if (value == default || value.Year > DateTime.Now.Year)
                throw new ArgumentException("El año de nacimiento no puede ser mayor al año actual");
            _fechaNacimiento = value;
        }
    }

    public PersonaAbstracta(string nombreCompleto, DateOnly fechaNacimiento)
    {
        NombreCompleto = nombreCompleto;
        FechaNacimiento = fechaNacimiento;
    }
    public abstract void MostrarInformacion();
    public int CalcularEdad()
    {
        return DateTime.Now.Year - FechaNacimiento.Year;

    }

    public abstract double CalcularPromedio();
}

public class EstudianteAbstracto : PersonaAbstracta
{
    private string _solapin;
    private List<Asignatura> asignaturas { get; set; }

    public List<Asignatura> Asignaturas
    {
        get { return asignaturas; }
        set
        {
            if (asignaturas.Count == 0)
                throw new ArgumentException("El estudiante debe tener al menos una asignatura");
            asignaturas = value;
        }
    }

    public string Solapin
    {
        get { return _solapin; }
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("El solapin no puede estar vacío");
            _solapin = value;
        }

    }
    public EstudianteAbstracto(string NombreCompleto, DateOnly fechaNacimiento, string _solapin, List<Asignatura> asignaturas) : base(NombreCompleto, fechaNacimiento)
    {
        this._solapin = _solapin;
        this.asignaturas = asignaturas;
    }

    public override void MostrarInformacion()
    {
        Console.WriteLine($"Los datos del estudiante son: ------------");
        Console.WriteLine($"Nombre Completo: {NombreCompleto}");
        Console.WriteLine($"Fecha de Nacimiento: {FechaNacimiento}");
        Console.WriteLine($"Solapin: {Solapin}");
        Console.WriteLine($"Total de Asignaturas Matriculadas: {Asignaturas.Count}");
        Console.WriteLine($"Notas:----------- ");
        foreach (Asignatura asignatura in Asignaturas)
        {
            Console.WriteLine($"Asignatura: {asignatura.NombreAsignatura}, Nota: {asignatura.Nota}");
        }

    }
    public override double CalcularPromedio()
    {
        double sum = 0;
        foreach (Asignatura asignatura in Asignaturas)
        {
            sum += asignatura.Nota;
        }
        return sum / Asignaturas.Count;
    }
}

public class Asignatura
{
    private string _nombreAsignatura { get; set; }= string.Empty;
    private int _notaFinal;

    public string NombreAsignatura
    {
        get { return _nombreAsignatura; }
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException("El nombre de la asignatura no puede estar vacío");
            _nombreAsignatura = value;
        }
    }

    public int Nota
    {
        get { return _notaFinal; }
        set
        {
            if (value < 2 || value > 10)
                throw new ArgumentException("La nota debe estar entre 0 y 10");
            _notaFinal = value;
        }
    }

    public Asignatura(string nombreAsignatura, int nota)
    {
        NombreAsignatura = nombreAsignatura;
        Nota = nota;
    }

}