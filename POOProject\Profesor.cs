using System;
using System.Collections.Generic;

namespace POOProject
{
    /// <summary>
    /// Clase que representa un Profesor
    /// Demuestra HERENCIA al heredar de la clase Persona
    /// Permite comparar diferentes implementaciones de los métodos abstractos
    /// </summary>
    public class Profesor : Persona
    {
        // Campos privados específicos del profesor
        private string _numeroEmpleado;
        private string _departamento;
        private decimal _salarioBase;
        private List<string> _materiasImpartidas;
        private int _anosExperiencia;

        public Profesor(string nombre, string apellido, int edad, string identificacion,
                       string numeroEmpleado, string departamento, decimal salarioBase, int anosExperiencia)
            : base(nombre, apellido, edad, identificacion)
        {
            // Validar edad mínima para profesores
            ValidarEdadMinima(22);

            _numeroEmpleado = numeroEmpleado ?? throw new ArgumentNullException(nameof(numeroEmpleado));
            _departamento = departamento ?? throw new ArgumentNullException(nameof(departamento));
            _salarioBase = salarioBase;
            _anosExperiencia = anosExperiencia;
            _materiasImpartidas = new List<string>();
        }

        // Propiedades específicas del profesor
        public string NumeroEmpleado
        {
            get { return _numeroEmpleado; }
            private set { _numeroEmpleado = value; }
        }

        public string Departamento
        {
            get { return _departamento; }
            set 
            { 
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("El departamento no puede estar vacío");
                _departamento = value; 
            }
        }

        public decimal SalarioBase
        {
            get { return _salarioBase; }
            set 
            { 
                if (value < 0)
                    throw new ArgumentException("El salario no puede ser negativo");
                _salarioBase = value; 
            }
        }

        public int AnosExperiencia
        {
            get { return _anosExperiencia; }
            set 
            { 
                if (value < 0)
                    throw new ArgumentException("Los años de experiencia no pueden ser negativos");
                _anosExperiencia = value; 
            }
        }

        public IReadOnlyList<string> MateriasImpartidas => _materiasImpartidas.AsReadOnly();

        // Implementación del método abstracto
        public override void MostrarRol()
        {
            Console.WriteLine($"Rol: Profesor del departamento de {_departamento}");
            Console.WriteLine($"Número de empleado: {_numeroEmpleado}");
            Console.WriteLine($"Años de experiencia: {_anosExperiencia}");
            Console.WriteLine($"Materias que imparte: {(_materiasImpartidas.Count > 0 ? string.Join(", ", _materiasImpartidas) : "Ninguna asignada")}");
        }

        // Implementación del método abstracto para calcular cuota (en este caso, salario)
        public override decimal CalcularCuota()
        {
            // Calcular salario con bonificaciones por experiencia
            decimal bonificacion = 0;
            
            if (_anosExperiencia >= 20)
                bonificacion = 0.30m; // 30% de bonificación
            else if (_anosExperiencia >= 10)
                bonificacion = 0.20m; // 20% de bonificación
            else if (_anosExperiencia >= 5)
                bonificacion = 0.10m; // 10% de bonificación

            // Bonificación adicional por número de materias
            decimal bonificacionMaterias = _materiasImpartidas.Count * 0.05m; // 5% por materia

            return _salarioBase * (1 + bonificacion + bonificacionMaterias);
        }

        // Sobrescribir el método virtual
        public override void MostrarInformacion()
        {
            base.MostrarInformacion();
            Console.WriteLine($"Departamento: {_departamento}");
            Console.WriteLine($"Número de empleado: {_numeroEmpleado}");
            Console.WriteLine($"Años de experiencia: {_anosExperiencia}");
            Console.WriteLine($"Salario mensual: ${CalcularCuota():F2}");
            Console.WriteLine($"Materias asignadas: {_materiasImpartidas.Count}");
        }

        // Métodos específicos del profesor
        public void AsignarMateria(string materia)
        {
            if (string.IsNullOrWhiteSpace(materia))
                throw new ArgumentException("El nombre de la materia no puede estar vacío");

            if (!_materiasImpartidas.Contains(materia))
            {
                _materiasImpartidas.Add(materia);
                Console.WriteLine($"Materia '{materia}' asignada correctamente");
            }
            else
            {
                Console.WriteLine($"La materia '{materia}' ya está asignada");
            }
        }

        public void RemoverMateria(string materia)
        {
            if (_materiasImpartidas.Remove(materia))
            {
                Console.WriteLine($"Materia '{materia}' removida correctamente");
            }
            else
            {
                Console.WriteLine($"La materia '{materia}' no estaba asignada");
            }
        }

        public void MostrarCargaAcademica()
        {
            Console.WriteLine("\n--- Carga Académica ---");
            if (_materiasImpartidas.Count == 0)
            {
                Console.WriteLine("No tiene materias asignadas");
                return;
            }

            Console.WriteLine($"Total de materias: {_materiasImpartidas.Count}");
            for (int i = 0; i < _materiasImpartidas.Count; i++)
            {
                Console.WriteLine($"{i + 1}. {_materiasImpartidas[i]}");
            }
        }
    }
}
