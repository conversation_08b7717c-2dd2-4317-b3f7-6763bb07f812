using System;
using System.Collections.Generic;

namespace POOProject
{
    class Program
    {
        static void Main(string[] args)
        {
            Grupo g1 = new ("Grupo1");

            Asignatura a1 = new("Math", 5);
            Asignatura a2 = new("Algebra", 3);
            Asignatura a3 = new("Fisica", 2);

            List<Asignatura> listaAsignaturas = [a1, a2, a3];

            PersonaAbstracta p1 = new EstudianteAbstracto("pepe", new DateOnly(1984, 1, 1), "F3-504", listaAsignaturas);
            PersonaAbstracta p2 = new EstudianteAbstracto("Jose", new DateOnly(2000, 1, 1), "F3-504", [a1, a2]);

            //g1.RegistroEstudiantes();
            Console.WriteLine($" {g1.RegistroEstudiantes()}");




            PersonaAbstracta p = new EstudianteAbstracto("pepe", new DateOnly(1984, 1, 1), "F3-504", listaAsignaturas);
            p.MostrarInformacion();
            //double promedio= p.CalcularPromedio();
            Console.WriteLine($"El promedio de nota es : {p.CalcularPromedio()}");
            Console.WriteLine($"La edad es : {p.CalcularEdad()}  años");
           


            /* Console.WriteLine("=== DEMOSTRACIÓN DE CONCEPTOS DE POO EN C# ===");
            Console.WriteLine("Herencia, Encapsulamiento y Abstracción\n");

            try
            {
                // Crear instancias de estudiantes
                Console.WriteLine("--- CREANDO ESTUDIANTES ---");

                var estudiante1 = new Estudiante(
                    "Ana", "García", 20, "12345678",
                    "EST001", "Ingeniería de Sistemas", 4, 2500000m
                );

                var estudiante2 = new Estudiante(
                    "Carlos", "Rodríguez", 19, "87654321",
                    "EST002", "Medicina", 2, 3000000m
                );

                // Agregar calificaciones
                estudiante1.AgregarCalificacion(4.5);
                estudiante1.AgregarCalificacion(4.2);
                estudiante1.AgregarCalificacion(4.8);
                estudiante1.AgregarCalificacion(4.0);

                estudiante2.AgregarCalificacion(3.8);
                estudiante2.AgregarCalificacion(3.5);
                estudiante2.AgregarCalificacion(4.1);

                // Crear profesor
                Console.WriteLine("\n--- CREANDO PROFESOR ---");

                var profesor1 = new Profesor(
                    "Dr. María", "López", 45, "11223344",
                    "PROF001", "Ingeniería", 4500000m, 15
                );

                // Asignar materias al profesor
                profesor1.AsignarMateria("Programación Orientada a Objetos");
                profesor1.AsignarMateria("Estructuras de Datos");
                profesor1.AsignarMateria("Algoritmos");

                // Demostrar polimorfismo usando la clase base
                Console.WriteLine("\n--- DEMOSTRACIÓN DE POLIMORFISMO ---");
                List<Persona> personas = new List<Persona>
                {
                    estudiante1,
                    estudiante2,
                    profesor1
                };

                foreach (Persona persona in personas)
                {
                    Console.WriteLine("\n" + new string('=', 50));
                    persona.MostrarInformacion(); // Método virtual sobrescrito
                    persona.MostrarRol(); // Método abstracto implementado
                    Console.WriteLine($"Cuota/Salario: ${persona.CalcularCuota():F2}");
                }

                // Demostrar funcionalidades específicas
                Console.WriteLine("\n" + new string('=', 60));
                Console.WriteLine("--- FUNCIONALIDADES ESPECÍFICAS ---");

                // Funcionalidades del estudiante
                Console.WriteLine("\n--- ESTUDIANTE 1 ---");
                estudiante1.MostrarCalificaciones();
                Console.WriteLine($"Estado académico: {estudiante1.EstadoAcademico}");
                Console.WriteLine($"¿En peligro académico?: {(estudiante1.EstaEnPeligroAcademico() ? "Sí" : "No")}");

                Console.WriteLine("\n--- ESTUDIANTE 2 ---");
                estudiante2.MostrarCalificaciones();
                estudiante2.AvanzarSemestre();

                // Funcionalidades del profesor
                Console.WriteLine("\n--- PROFESOR ---");
                profesor1.MostrarCargaAcademica();

                // Demostrar encapsulamiento - intentar acceder a campos privados
                Console.WriteLine("\n--- DEMOSTRACIÓN DE ENCAPSULAMIENTO ---");
                Console.WriteLine("Los campos privados no son accesibles directamente:");
                Console.WriteLine($"Nombre completo del estudiante: {estudiante1.NombreCompleto}");
                Console.WriteLine($"Número de estudiante (solo lectura): {estudiante1.NumeroEstudiante}");

                // Demostrar validaciones
                Console.WriteLine("\n--- DEMOSTRACIÓN DE VALIDACIONES ---");
                try
                {
                    estudiante1.Edad = -5; // Esto debería lanzar una excepción
                }
                catch (ArgumentException ex)
                {
                    Console.WriteLine($"Error capturado: {ex.Message}");
                }

                try
                {
                    estudiante1.AgregarCalificacion(6.0); // Calificación inválida
                }
                catch (ArgumentException ex)
                {
                    Console.WriteLine($"Error capturado: {ex.Message}");
                }

                // Demostrar abstracción - no se puede instanciar Persona directamente
                Console.WriteLine("\n--- DEMOSTRACIÓN DE ABSTRACCIÓN ---");
                Console.WriteLine("No se puede crear una instancia directa de la clase abstracta Persona");
                Console.WriteLine("Solo se pueden crear instancias de las clases derivadas (Estudiante, Profesor)");

                Console.WriteLine("\n--- RESUMEN DE CONCEPTOS DEMOSTRADOS ---");
                Console.WriteLine("✓ HERENCIA: Estudiante y Profesor heredan de Persona");
                Console.WriteLine("✓ ENCAPSULAMIENTO: Campos privados con propiedades públicas");
                Console.WriteLine("✓ ABSTRACCIÓN: Clase abstracta Persona con métodos abstractos");
                Console.WriteLine("✓ POLIMORFISMO: Diferentes implementaciones de métodos abstractos");
                Console.WriteLine("✓ SOBRESCRITURA: Métodos virtuales sobrescritos en clases derivadas");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }

            Console.WriteLine("\nPresiona cualquier tecla para salir...");
            Console.ReadKey();*/
        } 
    }
}
