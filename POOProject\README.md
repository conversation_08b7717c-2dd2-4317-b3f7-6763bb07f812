# Proyecto de Programación Orientada a Objetos en C#

Este proyecto demuestra los conceptos fundamentales de la Programación Orientada a Objetos (POO) en C#: **Herencia**, **Encapsulamiento** y **Abstracción**.

## 📋 Descripción

El proyecto implementa un sistema educativo básico con las siguientes clases:

- **Persona** (Clase abstracta base)
- **Estudiante** (Hereda de Persona)
- **Profesor** (Hereda de Persona)

## 🎯 Conceptos de POO Demostrados

### 1. **Herencia**
- Las clases `Estudiante` y `Profesor` heredan de la clase base `Persona`
- Reutilización de código y propiedades comunes
- Uso de constructores base con `base()`
- Llamadas a métodos de la clase padre con `base.MetodoBase()`

### 2. **Encapsulamiento**
- Campos privados (`_nombre`, `_apellido`, etc.)
- Propiedades públicas con validaciones
- Métodos protegidos para uso interno de la jerarquía
- Control de acceso a los datos

### 3. **Abstracción**
- Clase abstracta `Persona` que no puede ser instanciada directamente
- Métodos abstractos que deben ser implementados por las clases derivadas:
  - `MostrarRol()`
  - `CalcularCuota()`
- Ocultación de detalles de implementación

### 4. **Polimorfismo**
- Diferentes implementaciones de métodos abstractos en cada clase derivada
- Sobrescritura de métodos virtuales
- Uso de la clase base para referenciar objetos de clases derivadas

## 🏗️ Estructura del Proyecto

```
POOProject/
├── Persona.cs          # Clase abstracta base
├── Estudiante.cs       # Clase derivada para estudiantes
├── Profesor.cs         # Clase derivada para profesores
├── Program.cs          # Programa principal con demostraciones
├── POOProject.csproj   # Archivo de proyecto
└── README.md           # Este archivo
```

## 🚀 Cómo Ejecutar

1. **Prerrequisitos**: Tener instalado .NET 6.0 o superior

2. **Compilar el proyecto**:
   ```bash
   dotnet build
   ```

3. **Ejecutar el programa**:
   ```bash
   dotnet run
   ```

## 📊 Funcionalidades Implementadas

### Clase Persona (Abstracta)
- Propiedades: Nombre, Apellido, Edad, Identificación
- Métodos virtuales: `MostrarInformacion()`
- Métodos abstractos: `MostrarRol()`, `CalcularCuota()`
- Validaciones de datos
- Propiedad calculada: `NombreCompleto`

### Clase Estudiante
- Propiedades adicionales: NumeroEstudiante, Carrera, Semestre, CuotaSemestral
- Sistema de calificaciones con promedio automático
- Cálculo de descuentos por rendimiento académico
- Métodos específicos:
  - `AgregarCalificacion()`
  - `MostrarCalificaciones()`
  - `AvanzarSemestre()`
  - `EstaEnPeligroAcademico()`

### Clase Profesor
- Propiedades adicionales: NumeroEmpleado, Departamento, SalarioBase, AñosExperiencia
- Sistema de materias asignadas
- Cálculo de salario con bonificaciones
- Métodos específicos:
  - `AsignarMateria()`
  - `RemoverMateria()`
  - `MostrarCargaAcademica()`

## 🎮 Demostración Interactiva

El programa principal (`Program.cs`) incluye una demostración completa que muestra:

1. **Creación de objetos** de las clases derivadas
2. **Polimorfismo** usando referencias de la clase base
3. **Encapsulamiento** con validaciones automáticas
4. **Funcionalidades específicas** de cada clase
5. **Manejo de excepciones** para validaciones

## 📝 Ejemplos de Uso

```csharp
// Crear un estudiante
var estudiante = new Estudiante(
    "Ana", "García", 20, "12345678",
    "EST001", "Ingeniería de Sistemas", 4, 2500000m
);

// Agregar calificaciones
estudiante.AgregarCalificacion(4.5);
estudiante.AgregarCalificacion(4.2);

// Mostrar información (polimorfismo)
Persona persona = estudiante;
persona.MostrarInformacion(); // Llama al método sobrescrito
persona.MostrarRol();         // Llama al método implementado

// Calcular cuota con descuentos
decimal cuota = estudiante.CalcularCuota();
```

## 🔍 Características Técnicas

- **Validaciones robustas** en todas las propiedades
- **Manejo de excepciones** personalizado
- **Documentación XML** en el código
- **Principios SOLID** aplicados
- **Código limpio** y bien estructurado

## 📚 Conceptos Avanzados Incluidos

- Propiedades de solo lectura
- Propiedades calculadas
- Colecciones encapsuladas (`IReadOnlyList`)
- Métodos protegidos para herencia
- Sobrescritura de `ToString()`
- Uso de `nameof()` para validaciones

## 🎓 Valor Educativo

Este proyecto es ideal para:
- Aprender los fundamentos de POO en C#
- Entender la diferencia entre herencia e implementación
- Practicar el diseño de clases y jerarquías
- Comprender el encapsulamiento y la validación de datos
- Experimentar con polimorfismo y abstracción

---

**Autor**: Proyecto educativo de demostración de POO en C#  
**Versión**: 1.0  
**Framework**: .NET 9.0
